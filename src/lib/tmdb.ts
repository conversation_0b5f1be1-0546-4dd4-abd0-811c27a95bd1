/**
 * TMDB utility functions for data processing and formatting
 */

import { TMDBMovie, TMDBTVShow, TMDBGenre, TMDBImageSize, TMDBBackdropSize, TMDBProfileSize } from '@/types/tmdb';

const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p';

/**
 * Get full image URL from TMDB path
 */
export function getTMDBImageUrl(
  path: string | null,
  size: TMDBImageSize | TMDBBackdropSize | TMDBProfileSize = 'w500'
): string {
  if (!path) return '';
  return `${TMDB_IMAGE_BASE_URL}/${size}${path}`;
}

/**
 * Get poster URL with fallback
 */
export function getPosterUrl(
  posterPath: string | null,
  size: TMDBImageSize = 'w500'
): string {
  return getTMDBImageUrl(posterPath, size);
}

/**
 * Get backdrop URL with fallback
 */
export function getBackdropUrl(
  backdropPath: string | null,
  size: TMDBBackdropSize = 'w1280'
): string {
  return getTMDBImageUrl(backdropPath, size);
}

/**
 * Get profile image URL with fallback
 */
export function getProfileUrl(
  profilePath: string | null,
  size: TMDBProfileSize = 'w185'
): string {
  return getTMDBImageUrl(profilePath, size);
}

/**
 * Format runtime in minutes to hours and minutes
 */
export function formatRuntime(runtime: number | null): string {
  if (!runtime) return 'Unknown';
  
  const hours = Math.floor(runtime / 60);
  const minutes = runtime % 60;
  
  if (hours === 0) {
    return `${minutes}m`;
  }
  
  if (minutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${minutes}m`;
}

/**
 * Format release date to readable format
 */
export function formatReleaseDate(dateString: string | null): string {
  if (!dateString) return 'Unknown';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch {
    return 'Unknown';
  }
}

/**
 * Get year from date string
 */
export function getYear(dateString: string | null): string {
  if (!dateString) return 'Unknown';
  
  try {
    const date = new Date(dateString);
    return date.getFullYear().toString();
  } catch {
    return 'Unknown';
  }
}

/**
 * Format vote average to one decimal place
 */
export function formatVoteAverage(voteAverage: number): string {
  return voteAverage.toFixed(1);
}

/**
 * Get genre names from genre IDs
 */
export function getGenreNames(genreIds: number[], allGenres: TMDBGenre[]): string[] {
  return genreIds
    .map(id => allGenres.find(genre => genre.id === id)?.name)
    .filter(Boolean) as string[];
}

/**
 * Format genre list to comma-separated string
 */
export function formatGenres(genres: TMDBGenre[] | number[], allGenres?: TMDBGenre[]): string {
  if (Array.isArray(genres) && genres.length > 0) {
    if (typeof genres[0] === 'number' && allGenres) {
      // Handle genre IDs
      const genreNames = getGenreNames(genres as number[], allGenres);
      return genreNames.join(', ');
    } else {
      // Handle genre objects
      return (genres as TMDBGenre[]).map(genre => genre.name).join(', ');
    }
  }
  return 'Unknown';
}

/**
 * Check if content is a movie
 */
export function isMovie(content: TMDBMovie | TMDBTVShow): content is TMDBMovie {
  return 'title' in content && 'release_date' in content;
}

/**
 * Check if content is a TV show
 */
export function isTVShow(content: TMDBMovie | TMDBTVShow): content is TMDBTVShow {
  return 'name' in content && 'first_air_date' in content;
}

/**
 * Get title from movie or TV show
 */
export function getTitle(content: TMDBMovie | TMDBTVShow): string {
  if (isMovie(content)) {
    return content.title;
  }
  return content.name;
}

/**
 * Get release date from movie or TV show
 */
export function getReleaseDate(content: TMDBMovie | TMDBTVShow): string {
  if (isMovie(content)) {
    return content.release_date;
  }
  return content.first_air_date;
}

/**
 * Get original title from movie or TV show
 */
export function getOriginalTitle(content: TMDBMovie | TMDBTVShow): string {
  if (isMovie(content)) {
    return content.original_title;
  }
  return content.original_name;
}

/**
 * Truncate text to specified length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

/**
 * Get YouTube trailer URL from TMDB video key
 */
export function getYouTubeUrl(videoKey: string): string {
  return `https://www.youtube.com/watch?v=${videoKey}`;
}

/**
 * Get YouTube embed URL from TMDB video key
 */
export function getYouTubeEmbedUrl(videoKey: string): string {
  return `https://www.youtube.com/embed/${videoKey}`;
}

/**
 * Find trailer video from videos array
 */
export function findTrailer(videos: any[]): any | null {
  return videos.find(video => 
    video.type === 'Trailer' && 
    video.site === 'YouTube' && 
    video.official
  ) || videos.find(video => 
    video.type === 'Trailer' && 
    video.site === 'YouTube'
  ) || null;
}

/**
 * Calculate popularity score (0-100)
 */
export function getPopularityScore(popularity: number): number {
  // TMDB popularity is typically 0-1000+, normalize to 0-100
  return Math.min(Math.round((popularity / 1000) * 100), 100);
}

/**
 * Get content rating color based on vote average
 */
export function getRatingColor(voteAverage: number): string {
  if (voteAverage >= 8) return 'text-green-500';
  if (voteAverage >= 7) return 'text-yellow-500';
  if (voteAverage >= 6) return 'text-orange-500';
  return 'text-red-500';
}

/**
 * Sort content by popularity
 */
export function sortByPopularity<T extends { popularity: number }>(content: T[]): T[] {
  return [...content].sort((a, b) => b.popularity - a.popularity);
}

/**
 * Sort content by vote average
 */
export function sortByRating<T extends { vote_average: number }>(content: T[]): T[] {
  return [...content].sort((a, b) => b.vote_average - a.vote_average);
}

/**
 * Sort content by release date (newest first)
 */
export function sortByReleaseDate(content: (TMDBMovie | TMDBTVShow)[]): (TMDBMovie | TMDBTVShow)[] {
  return [...content].sort((a, b) => {
    const dateA = new Date(getReleaseDate(a));
    const dateB = new Date(getReleaseDate(b));
    return dateB.getTime() - dateA.getTime();
  });
}

/**
 * Filter content by genre
 */
export function filterByGenre<T extends { genre_ids: number[] }>(
  content: T[],
  genreId: number
): T[] {
  return content.filter(item => item.genre_ids.includes(genreId));
}

/**
 * Filter content by year
 */
export function filterByYear(
  content: (TMDBMovie | TMDBTVShow)[],
  year: number
): (TMDBMovie | TMDBTVShow)[] {
  return content.filter(item => {
    const releaseYear = parseInt(getYear(getReleaseDate(item)));
    return releaseYear === year;
  });
}

/**
 * Get unique genres from content array
 */
export function getUniqueGenres<T extends { genre_ids: number[] }>(
  content: T[],
  allGenres: TMDBGenre[]
): TMDBGenre[] {
  const genreIds = new Set<number>();
  content.forEach(item => {
    item.genre_ids.forEach(id => genreIds.add(id));
  });
  
  return allGenres.filter(genre => genreIds.has(genre.id));
}
