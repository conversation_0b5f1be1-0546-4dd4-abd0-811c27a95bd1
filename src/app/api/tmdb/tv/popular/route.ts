import { NextRequest, NextResponse } from 'next/server';
import { tmdbClient } from '@/api/tmdb';
import { TMDBResponse, TMDBTVShow } from '@/types/tmdb';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');

    if (page < 1 || page > 1000) {
      return NextResponse.json(
        { error: 'Page must be between 1 and 1000' },
        { status: 400 }
      );
    }

    const data: TMDBResponse<TMDBTVShow> = await tmdbClient.getPopularTVShows(page);

    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
      },
    });
  } catch (error) {
    console.error('Error fetching popular TV shows:', error);
    return NextResponse.json(
      { error: 'Failed to fetch popular TV shows' },
      { status: 500 }
    );
  }
}
