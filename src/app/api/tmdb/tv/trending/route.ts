import { NextRequest, NextResponse } from 'next/server';
import { tmdbClient } from '@/api/tmdb';
import { TMDBResponse, TMDBTVShow } from '@/types/tmdb';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeWindow = searchParams.get('time_window') as 'day' | 'week' || 'day';

    if (!['day', 'week'].includes(timeWindow)) {
      return NextResponse.json(
        { error: 'time_window must be "day" or "week"' },
        { status: 400 }
      );
    }

    const data: TMDBResponse<TMDBTVShow> = await tmdbClient.getTrendingTVShows(timeWindow);

    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600',
      },
    });
  } catch (error) {
    console.error('Error fetching trending TV shows:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trending TV shows' },
      { status: 500 }
    );
  }
}
