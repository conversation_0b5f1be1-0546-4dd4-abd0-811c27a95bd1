import { NextRequest, NextResponse } from 'next/server';
import { tmdbClient } from '@/api/tmdb';
import { TMDBResponse, TMDBMovie, TMDBTVShow } from '@/types/tmdb';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as 'movie' | 'tv' || 'movie';
    const page = parseInt(searchParams.get('page') || '1');
    const genre = searchParams.get('genre') ? parseInt(searchParams.get('genre')!) : undefined;
    const year = searchParams.get('year') ? parseInt(searchParams.get('year')!) : undefined;
    const sortBy = searchParams.get('sort_by') || undefined;

    if (!['movie', 'tv'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be "movie" or "tv"' },
        { status: 400 }
      );
    }

    if (page < 1 || page > 1000) {
      return NextResponse.json(
        { error: 'Page must be between 1 and 1000' },
        { status: 400 }
      );
    }

    if (year && (year < 1900 || year > new Date().getFullYear() + 5)) {
      return NextResponse.json(
        { error: 'Year must be between 1900 and current year + 5' },
        { status: 400 }
      );
    }

    const params = {
      page,
      genre,
      year,
      sortBy,
    };

    let data: TMDBResponse<TMDBMovie | TMDBTVShow>;

    if (type === 'movie') {
      data = await tmdbClient.discoverMovies(params);
    } else {
      data = await tmdbClient.discoverTVShows(params);
    }

    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
      },
    });
  } catch (error) {
    console.error('Error discovering content:', error);
    return NextResponse.json(
      { error: 'Failed to discover content' },
      { status: 500 }
    );
  }
}
