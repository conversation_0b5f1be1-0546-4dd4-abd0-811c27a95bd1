import { NextResponse } from 'next/server';

export async function GET() {
	try {
		const apiKey = process.env.TMDB_API_KEY;

		if (!apiKey) {
			return NextResponse.json(
				{ error: 'TMDB API key not found' },
				{ status: 500 }
			);
		}

		// Use the exact URL pattern from your curl example
		const url =
			'https://api.themoviedb.org/3/discover/movie?include_adult=true&include_video=trye&language=en-US&page=1&sort_by=popularity.desc&with_release_type=2|3';

		console.log('Testing discover endpoint with exact curl parameters:', url);

		const response = await fetch(url, {
			headers: {
				Accept: 'application/json',
				Authorization: `Bearer ${apiKey}`,
			},
		});

		console.log(
			'Discover response status:',
			response.status,
			response.statusText
		);

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Discover API Error:', errorText);
			return NextResponse.json(
				{
					error: 'TMDB Discover API Error',
					status: response.status,
					statusText: response.statusText,
					details: errorText,
				},
				{ status: response.status }
			);
		}

		const data = await response.json();

		return NextResponse.json({
			success: true,
			totalResults: data.total_results,
			resultsCount: data.results?.length || 0,
			firstMovie: data.results?.[0]?.title || 'No movies found',
			sampleResults:
				data.results?.slice(0, 3).map((movie: any) => ({
					id: movie.id,
					title: movie.title,
					release_date: movie.release_date,
					vote_average: movie.vote_average,
				})) || [],
		});
	} catch (error) {
		console.error('Test discover API failed:', error);
		return NextResponse.json(
			{
				error: 'Failed to test discover API',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}
