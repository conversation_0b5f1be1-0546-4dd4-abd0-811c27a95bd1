'use client';

import { TMDBMovie, TMDBTVShow } from '@/types/tmdb';
import { MediaCard } from './media-card';
import { cn } from '@/lib/utils';

interface MediaGridProps {
  media: (TMDBMovie | TMDBTVShow)[];
  className?: string;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
}

export function MediaGrid({ 
  media, 
  className,
  columns = {
    mobile: 2,
    tablet: 4,
    desktop: 6
  }
}: MediaGridProps) {
  const gridClasses = cn(
    'grid gap-4',
    {
      'grid-cols-1': columns.mobile === 1,
      'grid-cols-2': columns.mobile === 2,
      'grid-cols-3': columns.mobile === 3,
    },
    {
      'md:grid-cols-2': columns.tablet === 2,
      'md:grid-cols-3': columns.tablet === 3,
      'md:grid-cols-4': columns.tablet === 4,
      'md:grid-cols-5': columns.tablet === 5,
      'md:grid-cols-6': columns.tablet === 6,
    },
    {
      'lg:grid-cols-3': columns.desktop === 3,
      'lg:grid-cols-4': columns.desktop === 4,
      'lg:grid-cols-5': columns.desktop === 5,
      'lg:grid-cols-6': columns.desktop === 6,
      'lg:grid-cols-7': columns.desktop === 7,
      'lg:grid-cols-8': columns.desktop === 8,
    },
    className
  );

  if (media.length === 0) {
    return (
      <div className="flex h-32 items-center justify-center text-muted-foreground">
        No content found
      </div>
    );
  }

  return (
    <div className={gridClasses}>
      {media.map((item) => (
        <MediaCard key={item.id} media={item} />
      ))}
    </div>
  );
}

export default MediaGrid;
