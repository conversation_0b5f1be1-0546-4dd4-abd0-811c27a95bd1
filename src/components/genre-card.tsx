'use client';

import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { TMDBGenre } from '@/types/tmdb';
import { cn } from '@/lib/utils';

interface GenreCardProps {
  genre: TMDBGenre;
  mediaType: 'movie' | 'tv';
  className?: string;
}

const genreColors = [
  'bg-gradient-to-br from-red-500 to-red-600',
  'bg-gradient-to-br from-blue-500 to-blue-600',
  'bg-gradient-to-br from-green-500 to-green-600',
  'bg-gradient-to-br from-purple-500 to-purple-600',
  'bg-gradient-to-br from-yellow-500 to-yellow-600',
  'bg-gradient-to-br from-pink-500 to-pink-600',
  'bg-gradient-to-br from-indigo-500 to-indigo-600',
  'bg-gradient-to-br from-orange-500 to-orange-600',
  'bg-gradient-to-br from-teal-500 to-teal-600',
  'bg-gradient-to-br from-cyan-500 to-cyan-600',
];

export function GenreCard({ genre, mediaType, className }: GenreCardProps) {
  const href = `/discover/${mediaType}?genre=${genre.id}`;
  const colorIndex = genre.id % genreColors.length;
  const bgColor = genreColors[colorIndex];

  return (
    <Link href={href} className={className}>
      <Card className="group overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg">
        <CardContent className="p-0">
          <div className={cn(
            'flex h-24 items-center justify-center text-white transition-all duration-300 group-hover:brightness-110',
            bgColor
          )}>
            <h3 className="text-center font-semibold text-lg">
              {genre.name}
            </h3>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

export default GenreCard;
