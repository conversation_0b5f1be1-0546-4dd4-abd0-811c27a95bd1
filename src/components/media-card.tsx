'use client';

import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { TMDBMovie, TMDBTVShow } from '@/types/tmdb';
import { getPosterUrl, getTitle, getYear, getReleaseDate, formatVoteAverage, isMovie } from '@/lib/tmdb';
import { Star } from 'lucide-react';

interface MediaCardProps {
  media: TMDBMovie | TMDBTVShow;
  className?: string;
}

export function MediaCard({ media, className }: MediaCardProps) {
  const title = getTitle(media);
  const year = getYear(getReleaseDate(media));
  const posterUrl = getPosterUrl(media.poster_path, 'w342');
  const rating = formatVoteAverage(media.vote_average);
  const mediaType = isMovie(media) ? 'movie' : 'tv';
  const href = `/${mediaType}/${media.id}`;

  return (
    <Link href={href} className={className}>
      <Card className="group overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg">
        <CardContent className="p-0">
          <div className="relative aspect-[2/3] overflow-hidden">
            {posterUrl ? (
              <Image
                src={posterUrl}
                alt={title}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-110"
                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-muted">
                <span className="text-muted-foreground">No Image</span>
              </div>
            )}
            
            {/* Rating overlay */}
            {media.vote_average > 0 && (
              <div className="absolute top-2 right-2 flex items-center gap-1 rounded-md bg-black/70 px-2 py-1 text-xs text-white">
                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                <span>{rating}</span>
              </div>
            )}

            {/* Hover overlay */}
            <div className="absolute inset-0 bg-black/60 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            
            {/* Content overlay on hover */}
            <div className="absolute inset-x-0 bottom-0 translate-y-full p-4 text-white transition-transform duration-300 group-hover:translate-y-0">
              <h3 className="font-semibold leading-tight">{title}</h3>
              {year !== 'Unknown' && (
                <p className="text-sm text-gray-300">{year}</p>
              )}
              {media.overview && (
                <p className="mt-2 line-clamp-3 text-xs text-gray-300">
                  {media.overview}
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

export default MediaCard;
