/**
 * TMDB API Client
 * Handles all interactions with The Movie Database API
 */

const TMDB_BASE_URL = 'https://api.themoviedb.org/3';
const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p';

export interface TMDBConfig {
	apiKey: string;
	baseUrl?: string;
	imageBaseUrl?: string;
}

export class TMDBClient {
	private apiKey: string;
	private baseUrl: string;
	private imageBaseUrl: string;

	constructor(config: TMDBConfig) {
		this.apiKey = config.apiKey;
		this.baseUrl = config.baseUrl || TMDB_BASE_URL;
		this.imageBaseUrl = config.imageBaseUrl || TMDB_IMAGE_BASE_URL;
	}

	/**
	 * Make authenticated request to TMDB API with retry logic
	 */
	private async makeRequest<T>(
		endpoint: string,
		params: Record<string, string | number> = {},
		retries: number = 2
	): Promise<T> {
		const url = new URL(`${this.baseUrl}${endpoint}`);

		// Add default parameters
		url.searchParams.append('language', 'en-US');

		// Add additional parameters
		Object.entries(params).forEach(([key, value]) => {
			url.searchParams.append(key, value.toString());
		});

		for (let attempt = 0; attempt <= retries; attempt++) {
			try {
				console.log(`TMDB API Request (attempt ${attempt + 1}):`, {
					url: url.toString(),
					hasApiKey: !!this.apiKey,
				});

				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

				const response = await fetch(url.toString(), {
					headers: {
						Accept: 'application/json',
						'Content-Type': 'application/json',
						Authorization: `Bearer ${this.apiKey}`,
					},
					signal: controller.signal,
					next: { revalidate: 3600 }, // Cache for 1 hour
				});

				clearTimeout(timeoutId);

				console.log('TMDB API Response:', {
					status: response.status,
					statusText: response.statusText,
					ok: response.ok,
				});

				if (!response.ok) {
					const errorText = await response.text();
					console.error('TMDB API Error Response:', errorText);
					throw new Error(
						`TMDB API Error: ${response.status} ${response.statusText}`
					);
				}

				return await response.json();
			} catch (error) {
				console.error(
					`TMDB API Request failed (attempt ${attempt + 1}):`,
					error
				);

				// If this is the last attempt, throw the error
				if (attempt === retries) {
					throw error;
				}

				// Wait before retrying (exponential backoff)
				const delay = Math.pow(2, attempt) * 1000;
				console.log(`Retrying in ${delay}ms...`);
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}

		throw new Error('All retry attempts failed');
	}

	/**
	 * Get image URL with specified size
	 */
	getImageUrl(
		path: string,
		size:
			| 'w92'
			| 'w154'
			| 'w185'
			| 'w342'
			| 'w500'
			| 'w780'
			| 'original' = 'w500'
	): string {
		if (!path) return '';
		return `${this.imageBaseUrl}/${size}${path}`;
	}

	// Movie endpoints
	async getPopularMovies(page: number = 1): Promise<any> {
		return this.makeRequest('/movie/popular', { page });
	}

	async getTrendingMovies(timeWindow: 'day' | 'week' = 'day'): Promise<any> {
		return this.makeRequest(`/trending/movie/${timeWindow}`);
	}

	async getTopRatedMovies(page: number = 1): Promise<any> {
		return this.makeRequest('/movie/top_rated', { page });
	}

	async getUpcomingMovies(page: number = 1): Promise<any> {
		return this.makeRequest('/movie/upcoming', { page });
	}

	async getMovieDetails(movieId: number): Promise<any> {
		return this.makeRequest(`/movie/${movieId}`, {
			append_to_response: 'credits,videos,similar,recommendations',
		});
	}

	async getMovieCredits(movieId: number): Promise<any> {
		return this.makeRequest(`/movie/${movieId}/credits`);
	}

	async getMovieVideos(movieId: number): Promise<any> {
		return this.makeRequest(`/movie/${movieId}/videos`);
	}

	// TV Show endpoints
	async getPopularTVShows(page: number = 1): Promise<any> {
		return this.makeRequest('/tv/popular', { page });
	}

	async getTrendingTVShows(timeWindow: 'day' | 'week' = 'day'): Promise<any> {
		return this.makeRequest(`/trending/tv/${timeWindow}`);
	}

	async getTopRatedTVShows(page: number = 1): Promise<any> {
		return this.makeRequest('/tv/top_rated', { page });
	}

	async getTVShowDetails(tvId: number): Promise<any> {
		return this.makeRequest(`/tv/${tvId}`, {
			append_to_response: 'credits,videos,similar,recommendations',
		});
	}

	async getTVShowCredits(tvId: number): Promise<any> {
		return this.makeRequest(`/tv/${tvId}/credits`);
	}

	async getTVShowVideos(tvId: number): Promise<any> {
		return this.makeRequest(`/tv/${tvId}/videos`);
	}

	// Search endpoints
	async searchMovies(query: string, page: number = 1): Promise<any> {
		return this.makeRequest('/search/movie', { query, page });
	}

	async searchTVShows(query: string, page: number = 1): Promise<any> {
		return this.makeRequest('/search/tv', { query, page });
	}

	async searchMulti(query: string, page: number = 1): Promise<any> {
		return this.makeRequest('/search/multi', { query, page });
	}

	// Genre endpoints
	async getMovieGenres(): Promise<any> {
		return this.makeRequest('/genre/movie/list');
	}

	async getTVGenres(): Promise<any> {
		return this.makeRequest('/genre/tv/list');
	}

	async discoverMovies(
		params: {
			genre?: number;
			year?: number;
			sortBy?: string;
			page?: number;
		} = {}
	): Promise<any> {
		const queryParams: Record<string, string | number> = {};

		if (params.genre) queryParams.with_genres = params.genre;
		if (params.year) queryParams.year = params.year;
		if (params.sortBy) queryParams.sort_by = params.sortBy;
		if (params.page) queryParams.page = params.page;

		return this.makeRequest('/discover/movie', queryParams);
	}

	async discoverTVShows(
		params: {
			genre?: number;
			year?: number;
			sortBy?: string;
			page?: number;
		} = {}
	): Promise<any> {
		const queryParams: Record<string, string | number> = {};

		if (params.genre) queryParams.with_genres = params.genre;
		if (params.year) queryParams.first_air_date_year = params.year;
		if (params.sortBy) queryParams.sort_by = params.sortBy;
		if (params.page) queryParams.page = params.page;

		return this.makeRequest('/discover/tv', queryParams);
	}
}

// Create default client instance
const apiKey = process.env.TMDB_API_KEY || '';
console.log('TMDB Client Initialization:', {
	hasApiKey: !!apiKey,
	apiKeyLength: apiKey.length,
	apiKeyStart: apiKey.substring(0, 20) + '...',
});

export const tmdbClient = new TMDBClient({
	apiKey,
});

export default tmdbClient;
