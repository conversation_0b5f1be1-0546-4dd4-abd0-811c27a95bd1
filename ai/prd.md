# Streama - Product Requirements Document

## Project Overview

**Product Name:** Streama
**Version:** 1.0
**Date:** May 22, 2025
**Type:** Full-stack media aggregation and analytics application

## Executive Summary

Streama is a comprehensive media discovery and analytics platform that combines TMDB's extensive media catalog with Trakt.tv's user tracking capabilities. The application provides users with a Stremio-inspired interface for discovering movies and TV shows while offering detailed analytics about their viewing habits.

## Technical Specifications

### Frontend Stack

- **Framework:** Next.js 15 (App Router)
- **State Management:** Zustand
- **Forms:** TanStack Form
- **UI/Styling:** TailwindCSS + ShadCN UI
- **Charts:** Vega-Lite

### Backend Stack

- **API:** Next.js API Routes (monolithic)
- **Database:** PostgreSQL
- **ORM:** Prisma ORM
- **Authentication:** Better-auth (Google OAuth)
- **Caching:** Redis (optional enhancement)

### External APIs

- **TMDB API:** Media discovery and catalog data
- **Trakt.tv API:** User watch history and tracking

## Core Features

### 1. Discovery Dashboard (Home Page)

**Objective:** Provide users with an intuitive media discovery experience similar to Stremio

**Requirements:**

- Responsive grid layout (2 col mobile, 4 col tablet, 6 col desktop)
- Display sections for:

  - Popular Movies (TMDB) - horizontally scrollable
  - Popular Series (TMDB) - horizontally scrollable
  - Genre Categories (TMDB) - click on a genre card to visit a filtered view

- Media cards display:
  - Poster image
  - Title
  - Hover effects with fade-in animation
- Click-through to detailed item pages (movie/show page)

### 2. Media Detail Pages

**Objective:** Comprehensive media information and user actions

**Requirements:**

- Full-width poster header
- Complete metadata display:
  - Duration, Release Year, Genres
  - Directors, Cast information
  - Plot summary
- Action buttons:
  - "Watch Trailer"
  - "Add to Library"
- Responsive design across all devices

### 3. Analytics Dashboard

**Objective:** Visual analysis of user viewing patterns using Trakt.tv data

**Requirements:**

- Vega-Lite visualizations including:
  - Watch frequency (weekly/monthly/yearly)
  - Genre distribution charts
  - Time-of-day/weekday heatmaps
  - Top actors/directors analysis
  - Movie vs TV show time allocation
- Fallback state: Display connection prompt when no Trakt.tv API key exists
- Real-time data sync with Trakt.tv API

### 4. Search & Discovery

**Requirements:**

- Global search bar in top navigation
- Browse functionality:
  - By genre
  - Popular content
  - Trending content
- Advanced filtering:
  - Genre selection
  - Year ranges
- Search results with consistent card UI

### 5. User Settings

**Requirements:**

- API key configuration for:
  - TMDB API
  - Trakt.tv API
- User preference settings
- Account management integration

### 6. Personal Library

**Requirements:**

- User watchlist management
- Add/remove functionality
- Sync with Trakt.tv when available
- Persistent storage in database

## Data Architecture

### Database Schema Requirements

- User profiles and authentication
- Media watchlist storage
- Cached analytics data from Trakt.tv
- API configuration settings
- Session management

### API Integration

- **TMDB Endpoints:**
  - `/trending`, `/popular`, `/search`
  - `/movie/:id`, `/tv/:id`
  - Genre and metadata endpoints
- **Trakt.tv Endpoints:**
  - `/history` with filtering capabilities
  - `/sync` for watchlist management

## Authentication & Security

**Requirements:**

- Google OAuth integration via Better-auth
- Secure API key storage
- Session management
- Rate limiting for external API calls

## Performance Requirements

**Frontend:**

- Initial page load < 3 seconds
- Smooth animations and transitions
- Responsive design across all devices
- Progressive loading for media grids

**Backend:**

- API response times < 500ms
- Database query optimization
- Optional Redis caching for TMDB responses
- Efficient data synchronization with external APIs

## User Experience Requirements

### Navigation

- Intuitive top navigation bar
- Breadcrumb navigation for deep pages
- Responsive mobile navigation

### Visual Design

- Stremio-inspired UI patterns
- Consistent card-based layouts
- Dark theme optimization for media content
- Accessibility compliance (WCAG 2.1 AA)

### Error Handling

- Graceful fallbacks for API failures
- Clear error messages for users
- Offline state handling

## Success Metrics

**Engagement:**

- User session duration
- Pages per session
- Return user rate

**Functionality:**

- API integration success rates
- Search completion rates
- Library addition rates

**Performance:**

- Page load times
- API response times
- Error rates

## Technical Constraints

**Storage:**

- All persistent state must use database storage
- No localStorage for persistent data
- Efficient data caching strategies

**Architecture:**

- Monolithic Next.js application
- API routes for backend functionality
- Clear separation of concerns

## Future Enhancements

**Phase 2 Considerations:**

- Multi-platform streaming availability
- Social features and recommendations
- Advanced analytics and insights
- Mobile application development
- Enhanced caching with Redis implementation

## Acceptance Criteria

**Minimum Viable Product:**

- ✅ Complete TMDB integration for media discovery
- ✅ Functional Trakt.tv analytics dashboard
- ✅ User authentication and settings management
- ✅ Responsive design across all devices
- ✅ Personal library management
- ✅ Search and filtering capabilities

**Definition of Done:**

- All features tested across major browsers
- Performance benchmarks met
- Security audit completed
- Documentation finalized
- Deployment pipeline established
