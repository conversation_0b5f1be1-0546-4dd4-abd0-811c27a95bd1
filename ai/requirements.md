# Streama Requirements

A full-stack media aggregation and analytics application combining TMDB and Trakt.tv APIs. This document outlines the functional, UI, and technical requirements.

## 📛 App Name

**Streama**

## 🔧 Tech Stack

- **Frontend Framework**: Next.js 15 (App Router)
- **State Management**: Zustand
- **Forms**: TanStack Form (if viable with Next.js 15)
- **Styling/UI**: TailwindCSS + ShadCN UI
- **Database**: PostgreSQL + Drizzle ORM
- **Authentication**: Better-auth (Google OAuth)
- **Charts**: Vega-Lite (for watch behavior visualizations)
- **API Services**:

  - TMDB (discovery/catalog)
  - Trakt.tv (watch history/analytics)

## 🧭 Application Overview

### 1. 🏠 Home/Dashboard (Stremio-like UI)

- Layout:

  - Responsive grid layout:

    - Mobile: 1 column
    - Tablet: 2 columns
    - Desktop: 4–6 columns

- Sections:

  - **Popular Movies** (from TMDB)
  - **Popular Series** (from TMDB)
  - **Genre Categories**
  - “See All” links per category

- Card UI:

  - Poster image
  - Title
  - Short fade-in hover effect
  - <PERSON><PERSON> opens the detailed item page

### 2. 🎞️ Item Page (Movie/TV Show Detail)

- Mimics Stremio’s detail view:

  - Full-width poster header
  - Metadata: Duration, Year, Genres, Directors, Cast
  - Summary
  - Actions: “Watch Trailer”, “Add to Library”

### 3. 📊 Analytics Dashboard (Trakt.tv History)

- Visual analysis of watch habits using **Vega-Lite**:

  - Watch frequency by week/month/year
  - Genre distribution
  - Time-of-day/weekday heatmap
  - Top actors/directors
  - Movie vs show time split

- If no Trakt.tv key exists:

  - Display: **"No Watch Habit found. Please add your Trakt.tv API key to get started."**

## 🔌 API Usage

### TMDB

- Used for discovery/catalog
- Endpoints:

  - `/trending`, `/popular`, `/search`, `/movie/:id`, `/tv/:id`, etc.
  - Poster paths, metadata, genre lists

### Trakt.tv

- Used for tracking user viewing history

  ## Endpoints

  - `/history` - filtered by movie, show, date
  - /sync - to post watchlist to Trakt

## ⚙️ Features

### Discovery (TMDB)

- Search bar (top nav)
- Browse by genre, popular, trending
- Filters (genre, year)

### Detail Page

- Full metadata from TMDB
- Poster, trailer, summary, cast, etc.
- Add to personal watchlist (stored in backend)

### Analytics (Trakt.tv)

- Connect Trakt.tv account via API key (added in settings page)
- Sync watch history
- Generate graphs using Vega-Lite
- Store synced data in the database for later visualization

## 🧩 Settings Page

- TMDB and Trakt.tv API key configs

## 🗃️ Storage

### Database

- All persistent state must be stored in the database (watchlist, analytics cache, settings, etc)
- No usage of localStorage for persistent state

### Backend

- Use **Next.js API routes** (monolithic app)
- Handle:

  - Trakt.tv sync & storage
  - TMDB proxy/cache
  - User library
  - Auth/session

## 🧪 Optional Enhancements

- Server-side caching for TMDB responses (using Redis)
